<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\StudentController;
use App\Http\Controllers\Api\V1\DocumentController;
use App\Http\Controllers\Api\V1\CourseController;
use App\Http\Controllers\Api\V1\ProgressController;
use App\Http\Controllers\Api\V1\ChatController;
use App\Http\Controllers\Api\V1\FeeController;
use App\Http\Controllers\Api\V1\ReferralController;
use App\Http\Controllers\Api\V1\RewardController;

// API Version 1 Routes
Route::prefix('v1')->group(function () {

    // Public Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('admin/login', [AuthController::class, 'adminLogin']);
        Route::post('student/login', [AuthController::class, 'studentLogin']);
    });

    // Protected Admin Routes
    Route::middleware(['auth:sanctum', 'institute.access'])->prefix('admin')->group(function () {
        Route::post('logout', [AuthController::class, 'adminLogout']);
        Route::get('user', [AuthController::class, 'adminUser']);

        // Document management routes (specific routes first)
        Route::get('document-types', [DocumentController::class, 'getDocumentTypes']);
        Route::get('students/missing-documents', [DocumentController::class, 'getStudentsWithMissingDocuments']);
        Route::get('students/{student}/documents', [DocumentController::class, 'getStudentDocuments']);
        Route::post('documents/upload', [DocumentController::class, 'uploadDocument']);
        Route::put('documents/{document}/verify', [DocumentController::class, 'verifyDocument']);
        Route::get('documents/{document}/download', [DocumentController::class, 'downloadDocument']);
        Route::get('documents/pending', [DocumentController::class, 'getPendingDocuments']);
        Route::post('documents/request-missing', [DocumentController::class, 'requestMissingDocument']);

        // Student management routes
        Route::apiResource('students', StudentController::class);

        // Course management routes
        Route::apiResource('courses', CourseController::class);
        Route::post('courses/assign-student', [CourseController::class, 'assignToStudent']);

        // Progress tracking routes
        Route::get('progress/overview', [ProgressController::class, 'getProgressOverview']);
        Route::get('progress/stage/{stage}', [ProgressController::class, 'getStudentsByStage']);
        Route::put('students/{student}/progress', [ProgressController::class, 'updateStudentStage']);
        Route::get('students/{student}/progress-history', [ProgressController::class, 'getStudentProgressHistory']);

        // Chat routes
        Route::get('chat/conversations', [ChatController::class, 'getConversations']);
        Route::get('chat/messages/{student}', [ChatController::class, 'getMessages']);
        Route::post('chat/send', [ChatController::class, 'sendMessage']);
        Route::get('chat/unread-count', [ChatController::class, 'getUnreadCount']);
        Route::get('chat/download/{message}', [ChatController::class, 'downloadFile']);

        // Fee management routes
        Route::get('fees/overview', [FeeController::class, 'getFeeOverview']);
        Route::get('fees/students', [FeeController::class, 'getStudentFees']);
        Route::put('fees/update-payment', [FeeController::class, 'updatePayment']);

        // Referral management routes
        Route::prefix('referrals')->group(function () {
            Route::get('overview', [ReferralController::class, 'getOverview']);
            Route::get('codes', [ReferralController::class, 'getReferralCodes']);
            Route::post('codes', [ReferralController::class, 'createReferralCode']);
            Route::put('codes/{id}', [ReferralController::class, 'updateReferralCode']);
            Route::get('earnings', [ReferralController::class, 'getEarnings']);
            Route::post('payout', [ReferralController::class, 'requestPayout']);
            Route::get('analytics', [ReferralController::class, 'getAnalytics']);
        });

        // Reward points management routes
        Route::prefix('rewards')->group(function () {
            Route::get('overview', [RewardController::class, 'getOverview']);
            Route::get('student/{studentId}', [RewardController::class, 'getStudentPoints']);
            Route::post('award', [RewardController::class, 'awardPoints']);
            Route::post('deduct', [RewardController::class, 'deductPoints']);
            Route::post('bulk-award', [RewardController::class, 'bulkAwardPoints']);
            Route::get('transactions', [RewardController::class, 'getTransactions']);
            Route::get('statistics', [RewardController::class, 'getStatistics']);
        });

        // Institute management routes will be added here
        // Other admin routes will be added here
    });

    // Protected Student Routes
    Route::middleware(['auth:sanctum'])->prefix('student')->group(function () {
        Route::post('logout', [AuthController::class, 'studentLogout']);
        Route::get('user', [AuthController::class, 'studentUser']);

        // Student-specific routes will be added here
        // Document upload routes will be added here
        // Chat routes will be added here
        // Learning content routes will be added here
    });

    // Public routes (for student registration via referral)
    Route::prefix('public')->group(function () {
        // Student registration routes will be added here
    });
});

// Legacy route for backward compatibility
Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});
