<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Institute;
use App\Models\RewardPoint;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class RewardController extends BaseController
{
    /**
     * Get reward points overview for institute
     */
    public function getOverview(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $totalPointsAwarded = $institute->rewardPoints()->earned()->sum('points');
            $totalPointsRedeemed = abs($institute->rewardPoints()->spent()->sum('points'));
            $activePoints = $institute->rewardPoints()->active()->sum('points');
            $expiredPoints = $institute->rewardPoints()->expired()->earned()->sum('points');

            // Points breakdown by type
            $pointsByType = $institute->rewardPoints()
                ->selectRaw('type, SUM(points) as total_points, COUNT(*) as count')
                ->groupBy('type')
                ->get();

            // Top students by points
            $topStudents = Student::select('students.*')
                ->selectRaw('SUM(CASE WHEN reward_points.status = "active" AND (reward_points.expires_at IS NULL OR reward_points.expires_at > NOW()) THEN reward_points.points ELSE 0 END) as total_points')
                ->leftJoin('reward_points', 'students.id', '=', 'reward_points.student_id')
                ->where('students.institute_id', $institute->id)
                ->groupBy('students.id')
                ->orderBy('total_points', 'desc')
                ->limit(10)
                ->get();

            return $this->sendResponse([
                'overview' => [
                    'total_points_awarded' => $totalPointsAwarded,
                    'total_points_redeemed' => $totalPointsRedeemed,
                    'active_points' => $activePoints,
                    'expired_points' => $expiredPoints,
                ],
                'points_by_type' => $pointsByType,
                'top_students' => $topStudents,
            ], 'Reward points overview retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving reward points overview', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get reward points for a specific student
     */
    public function getStudentPoints(Request $request, $studentId)
    {
        try {
            $institute = $request->user()->institute;
            $student = $institute->students()->findOrFail($studentId);

            $pointsBreakdown = $student->reward_points_breakdown;
            
            $recentTransactions = $student->rewardPoints()
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();

            return $this->sendResponse([
                'student' => $student,
                'points_breakdown' => $pointsBreakdown,
                'recent_transactions' => $recentTransactions,
            ], 'Student reward points retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving student reward points', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Award points to a student
     */
    public function awardPoints(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'points' => 'required|integer|min:1',
                'type' => 'required|in:referral,course_completion,milestone,bonus,deduction,redemption',
                'description' => 'required|string|max:500',
                'expires_at' => 'nullable|date|after:now',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation Error', $validator->errors());
            }

            $institute = $request->user()->institute;
            $student = $institute->students()->findOrFail($request->student_id);

            $rewardPoint = RewardPoint::awardPoints(
                $institute->id,
                $student->id,
                $request->points,
                $request->type,
                $request->description,
                null,
                $request->expires_at
            );

            return $this->sendResponse($rewardPoint, 'Points awarded successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error awarding points', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Deduct points from a student
     */
    public function deductPoints(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'points' => 'required|integer|min:1',
                'type' => 'required|in:deduction,redemption',
                'description' => 'required|string|max:500',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation Error', $validator->errors());
            }

            $institute = $request->user()->institute;
            $student = $institute->students()->findOrFail($request->student_id);

            // Check if student has enough points
            $currentPoints = $student->total_reward_points;
            if ($currentPoints < $request->points) {
                return $this->sendError('Insufficient points', [
                    'current_points' => $currentPoints,
                    'requested_points' => $request->points
                ]);
            }

            $rewardPoint = RewardPoint::deductPoints(
                $institute->id,
                $student->id,
                $request->points,
                $request->type,
                $request->description
            );

            return $this->sendResponse($rewardPoint, 'Points deducted successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error deducting points', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get all reward point transactions
     */
    public function getTransactions(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $query = $institute->rewardPoints()->with(['student']);

            // Apply filters
            if ($request->has('student_id')) {
                $query->where('student_id', $request->student_id);
            }

            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('date_from')) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('created_at', '<=', $request->date_to);
            }

            $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

            return $this->sendResponse($transactions, 'Reward point transactions retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving reward point transactions', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Bulk award points to multiple students
     */
    public function bulkAwardPoints(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_ids' => 'required|array',
                'student_ids.*' => 'exists:students,id',
                'points' => 'required|integer|min:1',
                'type' => 'required|in:referral,course_completion,milestone,bonus',
                'description' => 'required|string|max:500',
                'expires_at' => 'nullable|date|after:now',
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation Error', $validator->errors());
            }

            $institute = $request->user()->institute;
            
            // Verify all students belong to the institute
            $students = $institute->students()->whereIn('id', $request->student_ids)->get();
            
            if ($students->count() !== count($request->student_ids)) {
                return $this->sendError('Some students do not belong to your institute');
            }

            DB::beginTransaction();

            $awardedPoints = [];
            foreach ($students as $student) {
                $rewardPoint = RewardPoint::awardPoints(
                    $institute->id,
                    $student->id,
                    $request->points,
                    $request->type,
                    $request->description,
                    null,
                    $request->expires_at
                );
                $awardedPoints[] = $rewardPoint;
            }

            DB::commit();

            return $this->sendResponse([
                'awarded_points' => $awardedPoints,
                'students_count' => count($awardedPoints),
                'total_points_awarded' => count($awardedPoints) * $request->points,
            ], 'Points awarded to all students successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Error awarding points to students', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get reward point statistics
     */
    public function getStatistics(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $startDate = $request->get('start_date', now()->subDays(30));
            $endDate = $request->get('end_date', now());

            // Points awarded in date range
            $pointsInRange = $institute->rewardPoints()
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('
                    SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as total_awarded,
                    SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as total_redeemed,
                    COUNT(CASE WHEN points > 0 THEN 1 END) as award_transactions,
                    COUNT(CASE WHEN points < 0 THEN 1 END) as redemption_transactions
                ')
                ->first();

            // Daily breakdown
            $dailyBreakdown = $institute->rewardPoints()
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('DATE(created_at) as date, SUM(points) as total_points, COUNT(*) as transactions')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return $this->sendResponse([
                'period_stats' => $pointsInRange,
                'daily_breakdown' => $dailyBreakdown,
            ], 'Reward point statistics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving reward point statistics', ['error' => $e->getMessage()]);
        }
    }
}
